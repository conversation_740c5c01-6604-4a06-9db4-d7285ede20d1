/**
 * 场站结算进度批量新增表格列配置
 * @param {Object} options - 下拉选项数据
 * @param {Array} options.settlementMethodOptions - 结算方式选项
 * @param {Array} options.settlementStatusOptions - 结算状态选项
 * @param {Array} options.investmentModeOptions - 投资模式选项
 * @returns {Array} 表格列配置数组
 */
export function getStationProgressColumns(options = {}) {
  const {
    settlementMethodOptions = [],
    settlementStatusOptions = [],
    investmentModeOptions = [],
  } = options;

  return [
    { type: "checkbox", width: 60, fixed: "left" },
    {
      title: "账单月份",
      field: "billYearMonth",
      width: 250,
      isEdit: true,
      element: "el-date-picker",
      rules: [{ required: true, message: "请选择账单年月" }],
      props: {
        type: "month",
        valueFormat: "yyyy-MM",
      },
    },
    {
      title: "运营商",
      field: "operator",
      width: 150,
      isEdit: true,
      element: "el-input",
      rules: [{ required: true, message: "请输入运营商" }],
    },
    {
      title: "站点名称",
      field: "siteName",
      width: 150,
      isEdit: true,
      element: "el-input",
      rules: [{ required: true, message: "请输入场站名称" }],
    },
    {
      title: "投资模式",
      field: "investmentMode",
      width: 120,
      isEdit: true,
      element: "el-select",
      props: {
        options: investmentModeOptions,
        optionLabel: "dictLabel",
        optionValue: "dictValue",
        filterable: true,
      },
    },
    {
      title: "结算方式",
      field: "settlementMethod",
      width: 120,
      isEdit: true,
      element: "el-select",
      props: {
        options: settlementMethodOptions,
        optionLabel: "dictLabel",
        optionValue: "dictValue",
        filterable: true,
      },
    },
    {
      title: "折扣（%）",
      field: "discount",
      width: 120,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 3,
        min: 0,
      },
    },
    {
      title: "充电量（kWh）",
      field: "chargingAmount",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 4,
        min: 0,
      },
    },
    {
      title: "实际充电费（元）",
      field: "actualChargingFee",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
        min: 0,
      },
    },
    {
      title: "放电量（kWh）",
      field: "dischargingAmount",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 4,
        min: 0,
      },
    },
    {
      title: "实际放电费（元）",
      field: "actualDischargingFee",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
        min: 0,
      },
    },
    {
      title: "电损率（%）",
      field: "powerLossRate",
      width: 120,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 3,
        min: 0,
      },
    },
    {
      title: "收益（元）",
      field: "income",
      width: 120,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
        min: 0,
      },
    },
    {
      title: "应付金额（元）",
      field: "payableAmount",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
        min: 0,
      },
    },
    {
      title: "应回金额（元）",
      field: "receivableAmount",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
        min: 0,
      },
    },
    {
      title: "出账日期",
      field: "billingDate",
      width: 150,
      isEdit: true,
      element: "el-date-picker",
      props: {
        type: "month",
        valueFormat: "yyyy-MM",
      },
    },
    {
      title: "回款日期",
      field: "paymentDate",
      width: 150,
      isEdit: true,
      element: "el-date-picker",
      props: {
        type: "month",
        valueFormat: "yyyy-MM",
      },
    },
    {
      title: "结算进度",
      field: "settlementStatus",
      width: 120,
      isEdit: true,
      element: "el-select",
      props: {
        options: settlementStatusOptions,
        optionLabel: "dictLabel",
        optionValue: "dictValue",
        filterable: true,
      },
    },
    {
      title: "对账负责人",
      field: "reconciliationPerson",
      width: 120,
      isEdit: true,
      element: "el-input",
    },
    {
      title: "备注",
      field: "remarks",
      width: 200,
      isEdit: true,
      element: "el-input",
      props: {
        type: "textarea",
        rows: 2,
        maxlength: 500,
        showWordLimit: true,
        placeholder: "500个字符以内",
      },
    },
  ];
}
