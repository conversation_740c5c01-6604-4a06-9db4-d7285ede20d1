/**
 * 场地回款统计批量新增表格列配置
 * @param {Object} options - 下拉选项数据
 * @returns {Array} 表格列配置数组
 */
export function getVenueStatisticsColumns(options = {}) {
  return [
    { type: "checkbox", width: 60, fixed: "left" },
    {
      title: "运营商名称",
      field: "operatorName",
      width: 150,
      isEdit: true,
      element: "el-input",
      rules: [{ required: true, message: "请输入运营商名称" }],
    },
    {
      title: "资金路径",
      field: "fundPath",
      width: 150,
      isEdit: true,
      element: "el-select",
      rules: [{ required: true, message: "请选择资金路径" }],
      props: {
        options: [
          { label: "直接付款", value: "直接付款" },
          { label: "托管付款", value: "托管付款" },
          { label: "第三方付款", value: "第三方付款" },
        ],
        filterable: true,
      },
    },
    {
      title: "应收总额（元）",
      field: "totalReceivable",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
        min: 0,
      },
    },
    {
      title: "托管回收金额（元）",
      field: "custodyRecoveryAmount",
      width: 180,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
        min: 0,
      },
    },
    {
      title: "线下付款金额（元）",
      field: "offlinePaymentAmount",
      width: 180,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
        min: 0,
      },
    },
    {
      title: "购电扣款（元）",
      field: "electricityPurchaseDeduction",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
        min: 0,
      },
    },
    {
      title: "已付款总额（元）",
      field: "totalPaymentAmount",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
        min: 0,
      },
    },
    {
      title: "待付款金额（元）",
      field: "pendingPaymentAmount",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
        min: 0,
      },
    },
    {
      title: "备注",
      field: "remarks",
      width: 200,
      isEdit: true,
      element: "el-input",
      props: {
        type: "textarea",
        rows: 2,
        maxlength: 500,
        showWordLimit: true,
        placeholder: "500个字符以内",
      },
    },
  ];
}
