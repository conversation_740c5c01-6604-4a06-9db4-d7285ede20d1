/**
 * 他投结算进度批量新增表格列配置
 * @param {Object} options - 下拉选项数据
 * @param {Array} options.reconciliationPersonOptions - 对账负责人选项
 * @returns {Array} 表格列配置数组
 */
export function getOtherProgressColumns(options = {}, methods = {}) {
  const {
    reconciliationPersonOptions = [],
    paymentProgressOptions = [],
    operatorOptions = [],
    investorNameOptions = [],
  } = options;
  const { querySearch } = methods;
  return [
    { type: "checkbox", width: 60, fixed: "left" },
    {
      title: "账单月份",
      field: "billYearMonth",
      width: 250,
      isEdit: true,
      element: "el-date-picker",
      rules: [{ required: true, message: "请选择账单月份" }],
      props: {
        type: "month",
        valueFormat: "yyyy-MM",
      },
    },

    {
      title: "运营商",
      field: "operator",
      width: 150,
      isEdit: true,
      element: "el-autocomplete",
      rules: [{ required: true, message: "请输入运营商" }],
      props: {
        fetchSuggestions: (queryString, cb) => {
          return querySearch(queryString, cb, operatorOptions);
        },
      },
    },
    {
      title: "站点名称",
      field: "siteName",
      width: 150,
      isEdit: true,
      element: "el-input",
      rules: [{ required: true, message: "请输入站点名称" }],
    },
    {
      title: "资方名称",
      field: "investorName",
      width: 150,
      isEdit: true,
      element: "el-autocomplete",
      props: {
        fetchSuggestions: (queryString, cb) => {
          return querySearch(queryString, cb, investorNameOptions);
        },
      },
    },
    {
      title: "场站比例（%）",
      field: "stationRatio",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 3,
      },
    },
    {
      title: "资方比例（%）",
      field: "investorRatio",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 3,
      },
    },
    {
      title: "平台比例（%）",
      field: "platformRatio",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 3,
      },
    },
    {
      title: "充电量（kWh）",
      field: "chargingAmount",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 4,
      },
    },
    {
      title: "充电费（元）",
      field: "chargingFee",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
      },
    },
    {
      title: "放电量（kWh）",
      field: "dischargingAmount",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 4,
      },
    },
    {
      title: "放电费（元）",
      field: "dischargingFee",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
      },
    },
    {
      title: "电损率（%）",
      field: "electricityRate",
      width: 120,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 3,
      },
    },
    {
      title: "能源管理收益金额（元）",
      field: "energyManagementIncome",
      width: 180,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
      },
    },
    {
      title: "能源管理服务费（元）",
      field: "energyManagementFee",
      width: 180,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
      },
    },
    {
      title: "资产收益（元）",
      field: "assetIncome",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
      },
    },
    {
      title: "平台收入（元）",
      field: "platformIncome",
      width: 150,
      isEdit: true,
      element: "el-input-number",
      props: {
        precision: 2,
      },
    },
    {
      title: "付款进度",
      field: "paymentProgress",
      width: 120,
      isEdit: true,
      element: "el-select",
      props: {
        options: paymentProgressOptions,
        optionLabel: "dictLabel",
        optionValue: "dictValue",
        filterable: true,
      },
    },
    {
      title: "负责人",
      field: "reconciliationPerson",
      width: 120,
      isEdit: true,
      element: "el-select",
      props: {
        options: reconciliationPersonOptions,
        optionLabel: "dictLabel",
        optionValue: "dictValue",
        filterable: true,
      },
    },
    {
      title: "备注",
      field: "remarks",
      width: 200,
      isEdit: true,
      element: "el-input",
      props: {
        type: "textarea",
        rows: 2,
        maxlength: 500,
        showWordLimit: true,
        placeholder: "500个字符以内",
      },
    },
  ];
}
